# GitHub Tool Documentation

The GitHub Tool provides functionality for cloning GitHub repositories and templates using Git and Copier.

## Overview

The `GitHubTool` class offers three main capabilities:
1. **Repository Cloning**: Clone any public GitHub repository using Git
2. **Template Cloning**: Clone and render GitHub-based Copier templates
3. **Repository Information**: Get information about local Git repositories

## Available Methods

### 1. `github_tool.clone_repository`

Clone a GitHub repository to a local directory.

**Parameters:**
- `repository_url` (string, required): GitHub repository URL (e.g., https://github.com/owner/repo)
- `destination_path` (string, required): Local path where the repository should be cloned
- `branch` (string, optional): Specific branch to clone
- `depth` (integer, optional): Shallow clone depth for faster cloning
- `recursive` (boolean, optional): Whether to clone submodules recursively

**Example Usage:**
```json
{
  "repository_url": "https://github.com/octocat/Hello-World",
  "destination_path": "/path/to/local/directory",
  "branch": "main",
  "depth": 1,
  "recursive": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully cloned repository 'Hello-World'",
  "repository_url": "https://github.com/octocat/Hello-World",
  "destination_path": "/path/to/local/directory",
  "branch": "main",
  "latest_commit": "abc123 Initial commit",
  "clone_options": {
    "depth": 1,
    "recursive": false
  }
}
```

### 2. `github_tool.clone_template`

Clone a GitHub template repository using Copier for template rendering.

**Parameters:**
- `template_url` (string, required): GitHub repository URL containing the Copier template
- `destination_path` (string, required): Local path where the template should be rendered
- `template_values` (object, optional): Dictionary of values to use for template rendering
- `branch` (string, optional): Specific branch/tag/commit to use
- `exclude_patterns` (array, optional): List of file patterns to exclude from copying
- `overwrite` (boolean, optional): Whether to overwrite existing files

**Example Usage:**
```json
{
  "template_url": "https://github.com/your-org/python-template",
  "destination_path": "/path/to/new/project",
  "template_values": {
    "project_name": "my-awesome-project",
    "author_name": "John Doe",
    "python_version": "3.11"
  },
  "branch": "v1.0.0",
  "exclude_patterns": ["*.pyc", "__pycache__"],
  "overwrite": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully generated template 'python-template' using Copier",
  "template_url": "https://github.com/your-org/python-template",
  "destination_path": "/path/to/new/project",
  "branch": "v1.0.0",
  "template_values": {
    "project_name": "my-awesome-project",
    "author_name": "John Doe",
    "python_version": "3.11"
  },
  "exclude_patterns": ["*.pyc", "__pycache__"],
  "files_generated": 25,
  "overwrite": false
}
```

### 3. `github_tool.list_repository_info`

Get information about a local Git repository.

**Parameters:**
- `repository_path` (string, required): Path to the local Git repository

**Example Usage:**
```json
{
  "repository_path": "/path/to/local/repository"
}
```

**Response:**
```json
{
  "success": true,
  "repository_info": {
    "repository_path": "/path/to/local/repository",
    "remote_url": "https://github.com/owner/repo.git",
    "current_branch": "main",
    "latest_commit": "abc123 Latest commit message",
    "status": "",
    "branches": "* main\n  feature-branch\n  remotes/origin/main",
    "modified_files": 0,
    "is_clean": true
  }
}
```

## Features

### URL Validation
- Automatically validates GitHub URLs
- Supports both `github.com` and `www.github.com`
- Handles URLs with or without `.git` suffix
- Extracts owner and repository information

### Error Handling
- Comprehensive error handling for all operations
- Timeout protection for long-running operations
- Detailed error messages with context
- Graceful handling of network issues

### Safety Features
- Checks for existing directories before cloning
- Prevents overwriting unless explicitly requested
- Validates destination paths
- Creates parent directories as needed

### Performance Optimizations
- Support for shallow cloning (`depth` parameter)
- Configurable timeouts (default: 10 minutes)
- Efficient command execution with proper resource limits

## Common Use Cases

### 1. Clone a Repository for Development
```json
{
  "repository_url": "https://github.com/fastapi/fastapi",
  "destination_path": "~/projects/fastapi",
  "depth": 1
}
```

### 2. Generate Project from Template
```json
{
  "template_url": "https://github.com/cookiecutter/cookiecutter-django",
  "destination_path": "~/projects/my-django-app",
  "template_values": {
    "project_name": "My Django App",
    "project_slug": "my-django-app",
    "author_name": "Your Name",
    "email": "<EMAIL>"
  }
}
```

### 3. Clone Specific Branch
```json
{
  "repository_url": "https://github.com/microsoft/vscode",
  "destination_path": "~/projects/vscode",
  "branch": "release/1.80",
  "depth": 1
}
```

### 4. Clone with Submodules
```json
{
  "repository_url": "https://github.com/git/git",
  "destination_path": "~/projects/git-source",
  "recursive": true
}
```

## Error Scenarios

### Invalid URL
```json
{
  "success": false,
  "error": "URL must be a GitHub repository URL",
  "repository_url": "https://gitlab.com/owner/repo",
  "destination_path": "/path/to/destination"
}
```

### Destination Exists
```json
{
  "success": false,
  "error": "Destination directory '/path/to/destination' already exists and is not empty",
  "repository_url": "https://github.com/owner/repo",
  "destination_path": "/path/to/destination"
}
```

### Network/Git Error
```json
{
  "success": false,
  "error": "Git clone failed: Repository not found",
  "repository_url": "https://github.com/owner/nonexistent-repo",
  "destination_path": "/path/to/destination",
  "command_output": "fatal: repository 'https://github.com/owner/nonexistent-repo.git/' not found"
}
```

## Requirements

### System Dependencies
- **Git**: Required for repository cloning
- **Copier**: Required for template cloning (`pip install copier`)

### Python Dependencies
- `pathlib`: For path manipulation
- `urllib.parse`: For URL parsing
- `asyncio`: For asynchronous command execution

## Security Considerations

### Safe Operations
- Only supports HTTPS GitHub URLs
- No support for SSH keys or authentication (public repos only)
- Path validation to prevent directory traversal
- Command injection protection through proper argument handling

### Limitations
- Only works with public GitHub repositories
- No authentication support for private repositories
- Limited to GitHub.com (no GitHub Enterprise support)

## Best Practices

### 1. Use Shallow Clones for Large Repositories
```json
{
  "repository_url": "https://github.com/torvalds/linux",
  "destination_path": "~/projects/linux",
  "depth": 1
}
```

### 2. Specify Branches for Stability
```json
{
  "repository_url": "https://github.com/nodejs/node",
  "destination_path": "~/projects/node",
  "branch": "v18.x"
}
```

### 3. Use Template Values for Customization
```json
{
  "template_url": "https://github.com/your-org/project-template",
  "destination_path": "~/projects/new-project",
  "template_values": {
    "project_name": "descriptive-project-name",
    "version": "0.1.0",
    "license": "MIT"
  }
}
```

### 4. Check Repository Info After Cloning
```json
{
  "repository_path": "~/projects/cloned-repo"
}
```

## Integration Examples

### Workflow Automation
The GitHub tool can be integrated into automation workflows for:
- Setting up development environments
- Creating projects from templates
- Synchronizing repositories
- Building deployment pipelines

### Template Management
- Maintain organization-wide project templates
- Standardize project structures
- Automate project initialization
- Version control template updates

This tool provides a robust foundation for GitHub-based operations in the Orchestra Template Engine ecosystem.
