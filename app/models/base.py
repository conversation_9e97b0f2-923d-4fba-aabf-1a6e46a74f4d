"""
base.py

Base model with UUID primary key and timestamp fields for all database models.

Author: <PERSON>
"""
from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4

from sqlmodel import Field, SQLModel


class BaseModel(SQLModel):
    """
    Base model that provides common fields for all database models.

    Features:
    - UUID primary key (non-optional)
    - created_at timestamp (automatically set on creation)
    - updated_at timestamp (automatically updated on modification)
    """

    id: UUID = Field(
        default_factory=uuid4, primary_key=True, description="Unique identifier"
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(), description="Creation timestamp"
    )
    updated_at: Optional[datetime] = Field(
        default=None, description="Last update timestamp"
    )

    def __init__(self, **data):
        """Initialize the model and set timestamps."""
        super().__init__(**data)
        # Set updated_at to created_at initially if not provided
        if self.updated_at is None:
            self.updated_at = self.created_at

    def update_timestamp(self):
        """Update the updated_at timestamp to current time."""
        self.updated_at = datetime.now()
