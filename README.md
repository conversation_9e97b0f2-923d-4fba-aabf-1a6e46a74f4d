# Orchestra Template Engine

A powerful Model Context Protocol (MCP) server with database integration, repository patterns, and GitHub operations for automation workflows.

## Features

- **MCP Server**: Tool-based interactions with GitHub, workflows, and scripts
- **Repository Pattern**: Clean data access layer with type safety
- **Database Integration**: PostgreSQL with SQLModel and Alembic migrations
- **GitHub Operations**: Repository cloning and template generation with Copier
- **Configuration Management**: Hierarchical configuration with environment support
- **Development Tools**: Comprehensive code quality and testing tools

## Quick Start

### Prerequisites
- Python 3.9+
- PostgreSQL database
- Git

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd orchestra

# Install dependencies
pip install -e .

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Initialize database
python -c "from app.db import init_db; init_db()"

# Run migrations
alembic upgrade head
```

### Running the Application

```bash
# Start MCP server
python app/server.py

# Or start FastAPI application
uvicorn app.main:app --reload
```

## Documentation

Comprehensive documentation is available in the [docs/](./docs/) directory:

- **[Main Documentation](./docs/README.md)** - Complete project overview
- **[MCP Server](./docs/mcp-server/README.md)** - Tool-based interactions
- **[Database Operations](./docs/database/README.md)** - PostgreSQL, SQLModel, and migrations
- **[Repository Pattern](./docs/repository/README.md)** - Data access layer
- **[Configuration](./docs/configuration/README.md)** - Settings and configuration
- **[Development](./docs/development/README.md)** - Development tools and processes

## Architecture

```
orchestra/
├── app/                    # Main application code
│   ├── mcp_server/        # MCP server and tools
│   ├── repository/        # Repository pattern implementation
│   ├── models/           # SQLModel database models
│   ├── api/              # FastAPI routes
│   └── settings.py       # Configuration system
├── docs/                 # Documentation
├── alembic/              # Database migrations
├── config/               # Configuration files
└── tests/                # Test suite
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Run tests: `pytest`
5. Run pre-commit checks: `pre-commit run --all-files`
6. Commit your changes: `git commit -m 'feat: add amazing feature'`
7. Push to the branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
