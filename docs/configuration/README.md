# Configuration Documentation

This section contains documentation for the Orchestra Template Engine configuration system.

## Overview

The configuration system provides a flexible, hierarchical approach to managing application settings with support for multiple sources and environment-specific configurations.

## Documentation

- **[Settings & Configuration](./settings.md)** - Complete guide to the configuration system, including structure, environment variables, and usage examples

## Quick Reference

### Configuration Priority

1. **Environment Variables** (highest priority)
2. **YAML Configuration File**
3. **Default Values** (lowest priority)

### Basic Usage

```python
from app.settings import get_settings, get_database_url

# Get complete settings
settings = get_settings()
print(f"Environment: {settings.environment}")

# Get specific configurations
db_url = get_database_url()
api_config = get_api_config()
```

### Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/orchestra
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Application
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# API
API_HOST=0.0.0.0
API_PORT=8000
```

### YAML Configuration

```yaml
# config/production.yaml
database:
  host: localhost
  port: 5432
  name: orchestra
  user: app_user

api:
  host: 0.0.0.0
  port: 8000
  debug: false

logging:
  level: INFO
  file_path: logs/app.log
```

## Configuration Sections

- **Database**: PostgreSQL connection settings
- **API**: FastAPI server configuration
- **Logging**: Application logging configuration
- **Security**: CORS and security settings
- **Environment**: Environment-specific settings

## Validation and Testing

```bash
# Validate configuration
python scripts/config_manager.py validate

# Test database connection
python scripts/config_manager.py test-db

# Display current configuration
python scripts/config_manager.py display
```
