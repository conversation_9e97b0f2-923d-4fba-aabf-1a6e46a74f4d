# Orchestra Template Engine Configuration

This document describes the configuration system for the Orchestra Template Engine, which provides a flexible and hierarchical approach to managing application settings.

## Overview

The configuration system supports multiple sources with the following priority order (highest to lowest):

1. **Environment Variables** - Override any setting
2. **YAML Configuration File** - Structured configuration
3. **Default Values** - Built-in defaults

## Configuration Structure

### Main Configuration Sections

- **`database`** - Database connection and pool settings
- **`api`** - FastAPI application settings
- **`logging`** - Logging configuration
- **`security`** - Security and CORS settings
- **`redis`** - Redis cache configuration

### Database Configuration

```yaml
database:
  host: localhost          # Database host
  port: 5432              # Database port
  name: orchestra         # Database name
  user: postgres          # Database user
  password: ""            # Database password (use env var)
  driver: postgresql      # Database driver
  echo: false            # Enable SQL logging
  pool_size: 10          # Connection pool size
  max_overflow: 20       # Max pool overflow
  pool_timeout: 30       # Pool timeout (seconds)
  pool_recycle: 3600     # Pool recycle time (seconds)
```

### API Configuration

```yaml
api:
  title: "Orchestra Template Engine"
  description: "API for managing templates and rendering them with values"
  version: "1.0.0"
  host: "0.0.0.0"
  port: 8000
  debug: false
  reload: false
  workers: 1
```

### Logging Configuration

```yaml
logging:
  level: INFO                    # Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: null               # Log file path (null = console only)
  max_file_size: 10485760       # Max log file size (10MB)
  backup_count: 5               # Number of backup files
```

### Security Configuration

```yaml
security:
  secret_key: "your-secret-key-change-in-production"
  algorithm: "HS256"
  access_token_expire_minutes: 30
  cors_origins: ["*"]
  cors_methods: ["*"]
  cors_headers: ["*"]
```

### Redis Configuration

```yaml
redis:
  host: localhost
  port: 6379
  db: 0
  password: null
  max_connections: 10
```

## Configuration Files

### Default Configuration File

The default configuration is loaded from `config/settings.yaml`:

```yaml
# config/settings.yaml
environment: development

database:
  host: localhost
  port: 5432
  name: orchestra
  user: postgres
  password: ""
  echo: false

api:
  host: "0.0.0.0"
  port: 8000
  debug: false

# ... other sections
```

### Environment-Specific Overrides

The configuration file can include environment-specific sections:

```yaml
# Base configuration
environment: development
database:
  host: localhost

# Environment-specific overrides
production:
  database:
    host: prod-db.example.com
    pool_size: 20
  api:
    workers: 4
    debug: false

staging:
  database:
    host: staging-db.example.com
  api:
    workers: 2
```

## Environment Variables

### Standard Environment Variables

All configuration values can be overridden using environment variables with the pattern `SECTION__KEY`:

```bash
# Database settings
DB__HOST=localhost
DB__PORT=5432
DB__NAME=orchestra
DB__USER=postgres
DB__PASSWORD=your_password

# API settings
API__HOST=0.0.0.0
API__PORT=8000
API__DEBUG=false

# Logging settings
LOG__LEVEL=INFO
LOG__FILE_PATH=/var/log/orchestra/app.log

# Security settings
SECURITY__SECRET_KEY=your-super-secret-key
```

### Special Environment Variables

- **`DATABASE_URL`** - Complete database URL (takes precedence over individual DB settings)
- **`ENVIRONMENT`** - Application environment (development, staging, production, testing)

```bash
# Complete database URL (overrides individual DB settings)
DATABASE_URL=************************************/database

# Application environment
ENVIRONMENT=production
```

## Usage Examples

### Basic Usage

```python
from app.config import get_settings, get_database_url, get_api_base_url

# Get complete settings
settings = get_settings()
print(f"Environment: {settings.environment}")

# Get specific URLs
db_url = get_database_url()
api_url = get_api_base_url()
```

### Loading Custom Configuration

```python
from app.config import get_settings

# Load from custom file
settings = get_settings("path/to/custom-config.yaml")
```

### Accessing Specific Configurations

```python
from app.config import (
    get_database_config,
    get_api_config,
    get_logging_config
)

# Get specific configuration sections
db_config = get_database_config()
api_config = get_api_config()
log_config = get_logging_config()

# Access properties
print(f"Database URL: {db_config.url}")
print(f"API Base URL: {api_config.base_url}")
```

### URL Generation

The configuration system automatically generates URLs from settings:

```python
from app.config import get_settings

settings = get_settings()

# Database URLs
print(f"Sync DB URL: {settings.database.url}")
print(f"Async DB URL: {settings.database.async_url}")

# API URLs
print(f"API Base URL: {settings.api.base_url}")

# Redis URL
print(f"Redis URL: {settings.redis.url}")
```

## Configuration Management Tools

### Configuration Manager Script

Use the configuration manager script for validation and testing:

```bash
# Validate configuration
python scripts/config_manager.py validate

# Display current configuration
python scripts/config_manager.py display --format yaml

# Test database connection
python scripts/config_manager.py test-db

# Display generated URLs
python scripts/config_manager.py urls

# Run all checks
python scripts/config_manager.py all

# Create sample configuration
python scripts/config_manager.py create-sample config/my-config.yaml
```

### Test Configuration

Run the configuration test suite:

```bash
python test_config.py
```

## Environment Setup

### Development Environment

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` with your settings:
   ```bash
   ENVIRONMENT=development
   DB__PASSWORD=your_dev_password
   API__DEBUG=true
   LOG__LEVEL=DEBUG
   ```

### Production Environment

1. Set environment variables in your deployment system:
   ```bash
   export ENVIRONMENT=production
   export DATABASE_URL=***********************************/orchestra
   export SECURITY__SECRET_KEY=your-production-secret-key
   export LOG__FILE_PATH=/var/log/orchestra/app.log
   ```

2. Use a production configuration file:
   ```yaml
   # config/production.yaml
   environment: production
   database:
     echo: false
     pool_size: 20
   api:
     debug: false
     workers: 4
   logging:
     level: WARNING
   ```

## Best Practices

### Security

1. **Never commit secrets** to version control
2. **Use environment variables** for sensitive data like passwords and secret keys
3. **Set strong secret keys** in production
4. **Restrict CORS origins** in production

### Performance

1. **Adjust pool settings** based on your load
2. **Use appropriate log levels** (WARNING or ERROR in production)
3. **Configure file logging** for production environments
4. **Set worker count** based on your server capacity

### Maintenance

1. **Validate configuration** before deployment
2. **Test database connections** after configuration changes
3. **Monitor log files** for configuration-related errors
4. **Use configuration management tools** for consistency

## Troubleshooting

### Common Issues

1. **Database connection fails**
   - Check `DATABASE_URL` or individual DB settings
   - Verify database server is running
   - Check network connectivity and firewall rules

2. **Configuration not loading**
   - Verify YAML syntax in configuration files
   - Check file permissions
   - Ensure configuration file path is correct

3. **Environment variables not working**
   - Use correct naming pattern: `SECTION__KEY`
   - Check for typos in variable names
   - Verify variables are exported in shell

### Debug Configuration

```python
from app.config import get_settings
import json

# Display current configuration
settings = get_settings()
print(json.dumps(settings.dict(), indent=2, default=str))

# Check specific values
print(f"Database URL: {settings.database.url}")
print(f"Environment: {settings.environment}")
```

### Validation

```python
from app.config import get_settings

try:
    settings = get_settings()
    print("✅ Configuration is valid")
except Exception as e:
    print(f"❌ Configuration error: {e}")
```
