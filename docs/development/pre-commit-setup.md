# Pre-commit Setup Guide

This guide explains how to set up and use the pre-commit hooks for the Orchestra Template Engine project.

## Quick Setup

### 1. Install Development Dependencies

```bash
# Option 1: Using pyproject.toml (recommended)
pip install -e .[dev]

# Option 2: Using requirements file
pip install -r requirements-dev.txt
```

### 2. Run Setup Script

```bash
python scripts/setup_pre_commit.py
```

This script will:
- Check Python version compatibility
- Install development dependencies
- Install and configure pre-commit hooks
- Run an initial check
- Create documentation

## Manual Setup

If you prefer to set up manually:

```bash
# Install pre-commit
pip install pre-commit

# Install git hooks
pre-commit install
pre-commit install --hook-type commit-msg

# Run on all files (optional)
pre-commit run --all-files
```

## Configured Tools

### Code Formatting
- **Black**: Automatic Python code formatting
- **isort**: Import statement sorting
- **autoflake**: Remove unused imports and variables
- **pyupgrade**: Upgrade Python syntax to newer versions

### Code Quality & Linting
- **flake8**: Python linting with multiple plugins
  - flake8-docstrings: Docstring conventions
  - flake8-bugbear: Additional bug and design problems
  - flake8-comprehensions: List/dict comprehension improvements
  - flake8-simplify: Code simplification suggestions
  - pep8-naming: PEP 8 naming conventions
- **pylint**: Comprehensive Python static analysis
- **mypy**: Static type checking

### Security
- **bandit**: Security vulnerability scanning
- **safety**: Check dependencies for known vulnerabilities
- **detect-private-key**: Prevent committing private keys

### File Quality
- **trailing-whitespace**: Remove trailing whitespace
- **end-of-file-fixer**: Ensure files end with newline
- **check-yaml**: YAML syntax validation
- **check-json**: JSON syntax validation
- **check-toml**: TOML syntax validation
- **yamllint**: YAML style and syntax linting
- **markdownlint**: Markdown style and syntax linting

### Git & Commits
- **check-merge-conflict**: Detect merge conflict markers
- **commitizen**: Enforce conventional commit messages

## Configuration Files

| File | Purpose |
|------|---------|
| `.pre-commit-config.yaml` | Main pre-commit configuration |
| `pyproject.toml` | Configuration for black, isort, mypy, bandit, pytest |
| `.flake8` | Flake8 linting configuration |
| `pylintrc` | Pylint static analysis configuration |
| `.yamllint.yaml` | YAML linting rules |
| `.markdownlint.yaml` | Markdown linting rules |

## Usage

### Automatic Execution

Pre-commit hooks run automatically when you commit:

```bash
git add .
git commit -m "feat: add new feature"
# Hooks run automatically and may modify files
# If hooks fail, commit is blocked
```

### Manual Execution

```bash
# Run all hooks on all files
pre-commit run --all-files

# Run specific hook
pre-commit run black
pre-commit run flake8
pre-commit run mypy

# Run on specific files
pre-commit run --files app/models/base.py app/models/workflow.py

# Run only on staged files
pre-commit run
```

### Skipping Hooks (Not Recommended)

```bash
# Skip all hooks (emergency only)
git commit --no-verify

# Skip specific hooks using SKIP environment variable
SKIP=pylint git commit -m "quick fix"
```

## Common Workflows

### First Time Setup

1. Clone repository
2. Create virtual environment: `python -m venv .venv`
3. Activate virtual environment: `source .venv/bin/activate`
4. Run setup: `python scripts/setup_pre_commit.py`
5. Make test commit to verify setup

### Daily Development

1. Make code changes
2. Stage files: `git add .`
3. Commit: `git commit -m "feat: description"`
4. Hooks run automatically and may:
   - Format code (black, isort)
   - Fix simple issues (autoflake, trailing-whitespace)
   - Report issues that need manual fixing

### Fixing Hook Failures

When hooks fail:

1. **Formatting issues**: Usually auto-fixed, just commit again
2. **Linting issues**: Fix reported problems manually
3. **Type issues**: Add type annotations or fix type errors
4. **Security issues**: Review and fix security concerns

### Updating Hooks

```bash
# Update to latest hook versions
pre-commit autoupdate

# Re-install after configuration changes
pre-commit install
```

## Troubleshooting

### Common Issues

**Hook installation fails**:
```bash
# Clear cache and reinstall
pre-commit clean
pre-commit install
```

**Hooks are slow**:
```bash
# Run specific hooks only
pre-commit run black isort flake8
```

**Type checking fails**:
```bash
# Install missing type stubs
pip install types-PyYAML types-requests
```

**Import order issues**:
- Check `.flake8` and `pyproject.toml` for isort configuration
- Ensure imports follow: stdlib, third-party, first-party, local

### Getting Help

1. Check configuration files for settings
2. Run hooks individually to isolate issues
3. Use `--verbose` flag for detailed output
4. Check tool documentation for specific error messages

## Best Practices

1. **Commit frequently**: Smaller commits are easier to fix if hooks fail
2. **Run hooks manually**: Test changes before committing
3. **Keep dependencies updated**: Regular `pre-commit autoupdate`
4. **Don't skip hooks**: They catch real issues
5. **Fix root causes**: Don't just silence warnings

## Integration with IDEs

### VS Code

Install extensions:
- Python
- Black Formatter
- Pylint
- MyPy Type Checker
- YAML
- Markdown All in One

Configure settings.json:
```json
{
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.linting.flake8Enabled": true,
    "editor.formatOnSave": true
}
```

### PyCharm

1. Configure Black as external tool
2. Enable Pylint inspection
3. Configure import optimization
4. Set up file watchers for auto-formatting

## Performance Tips

1. **Use file-specific hooks**: Some hooks only run on relevant files
2. **Parallel execution**: Pre-commit runs hooks in parallel when possible
3. **Cache utilization**: Hooks cache results for unchanged files
4. **Selective running**: Use `--files` to run on specific files only

This setup ensures consistent code quality and style across the entire project while catching potential issues early in the development process.
