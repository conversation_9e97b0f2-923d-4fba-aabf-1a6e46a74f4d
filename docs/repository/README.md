# Repository Pattern Documentation

This section contains documentation for the repository pattern implementation in the Orchestra Template Engine.

## Overview

The repository pattern provides a clean data access layer that separates business logic from database operations. It offers standardized CRUD operations, type safety, and comprehensive error handling.

## Documentation

- **[Repository Pattern Implementation](./repository-pattern.md)** - Complete guide to the repository pattern, including BaseRepository, custom repositories, and usage examples

## Quick Reference

### Basic Usage

```python
from app.repository import WorkflowRepository

# Initialize repository
repo = WorkflowRepository()

# Create record
workflow = repo.create({
    'name': 'My Workflow',
    'description': 'A sample workflow'
})

# Find records
workflows = repo.find_by(name='My Workflow')
recent = repo.get_recent_workflows(limit=5)

# Update record
updated = repo.update(workflow.id, {
    'description': 'Updated description'
})

# Pagination
paginated = repo.get_paginated(page=1, page_size=10)
```

### Creating Custom Repositories

```python
from app.repository.base_repository import BaseRepository
from app.models.your_model import YourModel

class YourRepository(BaseRepository[YourModel]):
    model = YourModel
    
    def find_by_status(self, status: str):
        return self.find_by(status=status)
```

## Available Repositories

- **WorkflowRepository**: Manages workflow data with custom business logic
- **BaseRepository**: Generic repository with standard CRUD operations

## Key Features

- **Type Safety**: Generic type support with `BaseRepository[ModelType]`
- **Automatic Session Management**: Handles database sessions and transactions
- **Pagination**: Built-in pagination with metadata
- **Search**: Text search across multiple fields
- **Bulk Operations**: Efficient batch operations
- **Error Handling**: Comprehensive error handling with proper cleanup
