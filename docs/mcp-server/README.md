# MCP Server Documentation

The Model Context Protocol (MCP) server provides tool-based interactions for the Orchestra Template Engine. This server implements various tools that can be called by MCP clients to perform operations like repository cloning, workflow management, and script execution.

## Overview

The MCP server is built using the FastMCP framework and provides a standardized interface for tool interactions. Each tool is implemented as a class that inherits from `BaseTool` and provides specific functionality.

## Architecture

```
app/mcp_server/
├── __init__.py           # Package initialization
├── server.py             # MCP server entry point
└── tools/                # Individual tool implementations
    ├── __init__.py       # Tool discovery and registration
    ├── base_tool.py      # Base tool class and decorators
    ├── github_tool.py    # GitHub operations
    ├── workflow_tool.py  # Workflow management
    └── script_tool.py    # Script execution
```

## Available Tools

### 1. GitHub Tool
**Namespace:** `github_tool`

Provides GitHub repository and template operations:
- `github_tool.clone_repository` - Clone GitHub repositories
- `github_tool.clone_template` - Clone and render Copier templates
- `github_tool.list_repository_info` - Get local repository information

[📖 Full Documentation](./github-tool.md)

### 2. Workflow Tool
**Namespace:** `workflow_tool`

Manages automation workflows:
- `workflow_tool.list_workflows` - List all workflows from database

### 3. Script Tool
**Namespace:** `script_tool`

Executes scripts and commands:
- `script_tool.list_predefined_scripts` - List available predefined scripts
- `script_tool.run_predefined_script` - Execute a predefined script
- `script_tool.run_custom_script` - Execute custom script code

## Tool Development

### Creating a New Tool

1. **Create the tool class:**
   ```python
   # app/mcp_server/tools/my_tool.py
   from .base_tool import BaseTool, tool_method
   
   class MyTool(BaseTool):
       @property
       def tool_name(self) -> str:
           return "my_tool"
       
       @property
       def tool_description(self) -> str:
           return "Description of my tool"
       
       @tool_method(name="my_method")
       async def my_method(self, param: str) -> dict:
           """My tool method."""
           return {"result": f"Processed: {param}"}
   ```

2. **Tool Discovery:**
   Tools are automatically discovered by the `setup_tools()` function in `__init__.py`. No manual registration is required.

3. **Naming Convention:**
   - Tool methods are exposed as `{tool_name}.{method_name}`
   - Example: `my_tool.my_method`

### Base Tool Features

The `BaseTool` class provides:
- **Automatic Discovery**: Tools are found automatically
- **Method Registration**: `@tool_method` decorator for method registration
- **Type Safety**: Full type hints support
- **Error Handling**: Standardized error handling patterns
- **Async Support**: Full async/await support

### Tool Method Decorator

The `@tool_method` decorator provides:
```python
@tool_method(name="method_name")
async def method_name(self, param1: str, param2: int = 10) -> dict:
    """Method description."""
    # Implementation
    return {"result": "success"}
```

## Server Configuration

### Starting the Server

```bash
# Start MCP server
python app/server.py
```

### Server Features

- **Tool Discovery**: Automatic discovery of all tool classes
- **Method Registration**: Automatic registration with FastMCP
- **Error Handling**: Comprehensive error handling and logging
- **Type Safety**: Full type checking and validation

### Integration with FastMCP

The server integrates with FastMCP to provide:
- **Standardized Protocol**: MCP-compliant tool interface
- **Client Compatibility**: Works with any MCP client
- **Transport Support**: Currently supports stdio transport

## Usage Examples

### From MCP Client

```json
{
  "method": "tools/call",
  "params": {
    "name": "github_tool.clone_repository",
    "arguments": {
      "repository_url": "https://github.com/octocat/Hello-World",
      "destination_path": "/tmp/hello-world"
    }
  }
}
```

### From Python Code

```python
from app.mcp_server.tools import setup_tools

# Set up tool manager
tool_manager = setup_tools()
registry = tool_manager.get_registry()

# Get available tools
tools = registry.list_tools()
methods = registry.list_tool_methods()

# Call a tool method
method = registry.get_tool_method("github_tool.clone_repository")
result = await method(
    repository_url="https://github.com/octocat/Hello-World",
    destination_path="/tmp/hello-world"
)
```

## Error Handling

### Standard Error Format

All tools return errors in a consistent format:
```json
{
  "success": false,
  "error": "Error description",
  "error_code": "ERROR_CODE",
  "details": {
    "additional": "context"
  }
}
```

### Common Error Types

- **Validation Errors**: Invalid parameters or configuration
- **Network Errors**: Connection or timeout issues
- **File System Errors**: Permission or path issues
- **Tool-Specific Errors**: Errors specific to tool functionality

## Testing

### Unit Testing

```bash
# Test individual tools
pytest tests/test_mcp_server/test_tools/

# Test specific tool
pytest tests/test_mcp_server/test_tools/test_github_tool.py
```

### Integration Testing

```bash
# Test complete server
pytest tests/test_mcp_server/test_server.py
```

### Manual Testing

```python
# Test tool discovery
from app.mcp_server.tools import setup_tools
tool_manager = setup_tools()
print("Tools:", tool_manager.get_registry().list_tools())

# Test specific tool
from app.mcp_server.tools.github_tool import GitHubTool
tool = GitHubTool()
result = await tool.clone_repository(
    repository_url="https://github.com/octocat/Hello-World",
    destination_path="/tmp/test"
)
print("Result:", result)
```

## Best Practices

### Tool Design

1. **Single Responsibility**: Each tool should have a clear, focused purpose
2. **Async Methods**: Use async/await for all tool methods
3. **Type Hints**: Provide complete type annotations
4. **Error Handling**: Handle errors gracefully with descriptive messages
5. **Documentation**: Include comprehensive docstrings

### Method Design

1. **Clear Names**: Use descriptive method names
2. **Consistent Returns**: Return consistent data structures
3. **Parameter Validation**: Validate all input parameters
4. **Resource Cleanup**: Properly clean up resources
5. **Timeout Handling**: Implement appropriate timeouts

### Security Considerations

1. **Input Validation**: Validate all user inputs
2. **Path Traversal**: Prevent directory traversal attacks
3. **Command Injection**: Sanitize command parameters
4. **Resource Limits**: Implement resource usage limits
5. **Error Information**: Don't leak sensitive information in errors

## Troubleshooting

### Tool Discovery Issues

```python
# Check tool discovery
from app.mcp_server.tools import setup_tools
try:
    tool_manager = setup_tools()
    print("✅ Tool discovery successful")
    print("Tools:", tool_manager.get_registry().list_tools())
except Exception as e:
    print("❌ Tool discovery failed:", e)
```

### Method Registration Issues

```python
# Check method registration
registry = tool_manager.get_registry()
methods = registry.list_tool_methods()
print("Registered methods:", methods)

# Check specific method
method = registry.get_tool_method("tool_name.method_name")
if method:
    print("✅ Method found")
else:
    print("❌ Method not found")
```

### Server Startup Issues

```bash
# Check server startup
python -c "from app.server import create_server; mcp, tm = create_server(); print('✅ Server created')"
```

## Contributing

When contributing new tools:

1. **Follow Naming Conventions**: Use clear, descriptive names
2. **Add Documentation**: Include comprehensive documentation
3. **Write Tests**: Add unit and integration tests
4. **Update Documentation**: Update this README with new tools
5. **Follow Code Style**: Use pre-commit hooks for code quality

---

For more information about specific tools, see their individual documentation files in this directory.
