# Database Documentation

This section contains documentation for database operations, migrations, and models in the Orchestra Template Engine.

## Overview

The Orchestra Template Engine uses PostgreSQL as its primary database with SQLModel for ORM operations and Alembic for schema migrations.

## Database Stack

- **PostgreSQL**: Primary database
- **SQLModel**: ORM and model definitions
- **Alembic**: Database migrations
- **pgvector**: Vector similarity search extension
- **asyncpg**: Async PostgreSQL driver

## Database Models

### BaseModel
All models inherit from `BaseModel` which provides:
- `id`: UUID primary key
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp
- `update_timestamp()`: Method to update the timestamp

### Available Models
- **Workflow**: Automation workflow management

## Migrations with Alembic

### Setup
Alembic is configured to work with the application's database settings and automatically detect model changes.

### Common Commands

```bash
# Create a new migration
alembic revision --autogenerate -m "description of changes"

# Apply migrations
alembic upgrade head

# Rollback one migration
alembic downgrade -1

# Show current migration status
alembic current

# Show migration history
alembic history

# Rollback to specific revision
alembic downgrade <revision_id>
```

### Migration Files
Migration files are stored in `alembic/versions/` and contain:
- **Upgrade functions**: Apply schema changes
- **Downgrade functions**: Rollback schema changes
- **Revision metadata**: Timestamps and dependencies

## Database Configuration

### Environment Variables
```bash
# Complete database URL
DATABASE_URL=postgresql://user:password@localhost:5432/orchestra

# Or individual components
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=orchestra
POSTGRES_USER=user
POSTGRES_PASSWORD=password
```

### Connection Management
The application uses connection pooling and automatic session management through the repository pattern.

## Database Initialization

### First Time Setup
```bash
# Initialize database tables
python -c "from app.db import init_db; init_db()"

# Run migrations
alembic upgrade head
```

### Development Reset
```bash
# Drop all tables and recreate
python -c "from app.db import reset_db; reset_db()"

# Run migrations
alembic upgrade head
```

## Vector Search with pgvector

The database includes pgvector extension for vector similarity search operations:

```sql
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Example vector column
ALTER TABLE workflows ADD COLUMN embedding vector(1536);
```

## Best Practices

### Model Design
1. **Inherit from BaseModel**: Use the base model for common fields
2. **Use Type Hints**: Provide complete type annotations
3. **Add Constraints**: Use SQLModel constraints for validation
4. **Document Fields**: Include field descriptions

### Migration Management
1. **Review Generated Migrations**: Always review auto-generated migrations
2. **Test Migrations**: Test both upgrade and downgrade paths
3. **Backup Before Major Changes**: Backup database before significant schema changes
4. **Use Descriptive Names**: Use clear, descriptive migration messages

### Performance
1. **Index Important Fields**: Add indexes for frequently queried fields
2. **Use Connection Pooling**: Leverage SQLModel's connection pooling
3. **Optimize Queries**: Use the repository pattern for efficient queries
4. **Monitor Performance**: Track query performance and optimize as needed

## Troubleshooting

### Connection Issues
```python
# Test database connection
from app.db import get_engine
engine = get_engine()
print("Database connected:", engine is not None)
```

### Migration Issues
```bash
# Check migration status
alembic current

# Resolve migration conflicts
alembic merge heads

# Force migration to specific revision
alembic stamp <revision_id>
```

### Model Issues
```python
# Test model creation
from app.models.workflow import Workflow
from app.db import init_db

init_db()
workflow = Workflow(name="test", description="test workflow")
print("Model created:", workflow)
```

## Schema Documentation

### Current Schema
The database schema is defined by the SQLModel models and managed through Alembic migrations. Key tables include:

- **workflows**: Automation workflow definitions
- **alembic_version**: Migration version tracking

### Schema Evolution
Schema changes are tracked through Alembic migrations, providing:
- **Version Control**: Track all schema changes
- **Rollback Capability**: Ability to rollback changes
- **Team Synchronization**: Consistent schema across environments
- **Production Safety**: Safe deployment of schema changes

For detailed model definitions, see the `app/models/` directory.
