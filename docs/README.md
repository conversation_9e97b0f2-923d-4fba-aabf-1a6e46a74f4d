# Orchestra Template Engine Documentation

Welcome to the comprehensive documentation for the Orchestra Template Engine - a powerful MCP (Model Context Protocol) server with database integration, repository patterns, and GitHub operations.

## Project Overview

The Orchestra Template Engine is a modern Python application built with:
- **FastAPI** for web API functionality
- **SQLModel** for database operations with PostgreSQL
- **MCP Server** for tool-based interactions
- **Repository Pattern** for clean data access
- **Alembic** for database migrations
- **Comprehensive tooling** for development and deployment

## Documentation Structure

### 🛠️ MCP Server & Tools
- **[GitHub Tool](./mcp-server/github-tool.md)** - Clone repositories and templates using Git and Copier

### 🗄️ Database & Repository
- **[Database Operations](./database/README.md)** - PostgreSQL, SQLModel, and Alembic migrations
- **[Repository Pattern](./repository/repository-pattern.md)** - Clean data access layer with standardized operations

### ⚙️ Configuration
- **[Settings & Configuration](./configuration/settings.md)** - Application configuration system

### 🔧 Development
- **[Pre-commit Setup](./development/pre-commit-setup.md)** - Code quality and formatting tools

## Quick Start

### Prerequisites
- Python 3.9+
- PostgreSQL database
- Git

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd orchestra
   ```

2. **Install dependencies:**
   ```bash
   pip install -e .
   ```

3. **Set up environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Initialize database:**
   ```bash
   python -c "from app.db import init_db; init_db()"
   ```

5. **Run database migrations:**
   ```bash
   alembic upgrade head
   ```

### Running the Application

#### MCP Server
```bash
python app/server.py
```

#### FastAPI Application
```bash
uvicorn app.main:app --reload
```

## Architecture Overview

```
orchestra/
├── app/                    # Main application code
│   ├── mcp_server/        # MCP server and tools
│   │   └── tools/         # Individual MCP tools
│   ├── repository/        # Repository pattern implementation
│   ├── models/           # SQLModel database models
│   ├── api/              # FastAPI routes
│   ├── db.py             # Database configuration
│   ├── settings.py       # Application settings
│   ├── main.py           # FastAPI application
│   └── server.py         # MCP server entry point
├── alembic/              # Database migrations
├── config/               # Configuration files
├── docs/                 # Documentation
├── scripts/              # Utility scripts
└── tests/                # Test suite
```

## Core Components

### MCP Server
The Model Context Protocol server provides tool-based interactions:
- **GitHub Tool**: Repository and template operations
- **Workflow Tool**: Workflow management
- **Script Tool**: Script execution

### Repository Pattern
Clean data access layer with:
- **BaseRepository**: Generic CRUD operations
- **Type Safety**: Generic type support
- **Transaction Management**: Automatic session handling
- **Pagination**: Built-in pagination support

### Database Models
SQLModel-based models with:
- **BaseModel**: Common fields (id, created_at, updated_at)
- **Workflow**: Workflow management
- **Automatic Migrations**: Alembic integration

### Configuration System
Hierarchical configuration with:
- **Environment Variables**: Highest priority
- **YAML Files**: Structured configuration
- **Default Values**: Built-in defaults

## Development Workflow

### Code Quality
The project uses comprehensive code quality tools:
- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking
- **pylint**: Static analysis
- **pre-commit**: Automated checks

### Testing
```bash
# Run tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test
pytest tests/test_specific.py
```

### Database Migrations
```bash
# Create new migration
alembic revision --autogenerate -m "description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

## API Documentation

When running the FastAPI application, interactive API documentation is available at:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Environment Variables

Key environment variables:
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/orchestra
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=orchestra
POSTGRES_USER=user
POSTGRES_PASSWORD=password

# Application
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# API
API_HOST=0.0.0.0
API_PORT=8000
```

## Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes**
4. **Run tests**: `pytest`
5. **Run pre-commit checks**: `pre-commit run --all-files`
6. **Commit your changes**: `git commit -m 'feat: add amazing feature'`
7. **Push to the branch**: `git push origin feature/amazing-feature`
8. **Open a Pull Request**

## Troubleshooting

### Common Issues

#### Database Connection
```bash
# Check database connection
python -c "from app.db import get_engine; print('Database connected:', get_engine() is not None)"
```

#### MCP Server
```bash
# Test MCP server tools
python -c "from app.mcp_server.tools import setup_tools; print('Tools:', setup_tools().get_registry().list_tools())"
```

#### Repository Pattern
```bash
# Test repository
python -c "from app.repository import WorkflowRepository; print('Repository working:', WorkflowRepository().count())"
```

## Support

For questions, issues, or contributions:
1. Check the specific component documentation
2. Review the troubleshooting section
3. Run the test suites
4. Check the GitHub issues

## License

This project is licensed under the MIT License - see the LICENSE file for details.

---

*This documentation is maintained alongside the Orchestra Template Engine codebase. For the latest updates, please refer to the repository.*
